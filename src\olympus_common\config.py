"""Configuration objects to be used in specific DDs or Data-handlers.

By adding `frozen=True` to the dataclass decorator, the instance becomes immutable.
By adding `metaclass=Singleton` to the class definition, the instance becomes a Singleton and can only be instantiated
once.
`env_field` is used to get the fields directly from the environment.
`dataclass_field` is for adding nested dataclasses on your dataclass.

Notes
-----
If support for multiple objects is required, remove the metaclass=Singleton and you are good to go.
"""

import json
import logging
import urllib.parse
import warnings
from dataclasses import dataclass
from pathlib import Path

import urllib3

from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.logger import MAX_FILE_SIZE_MB, get_level_name
from olympus_common.utils import Singleton, mb_to_bytes, pathstring, strtobool


@dataclass(frozen=True)
class ElasticConfig(metaclass=Singleton):
    """Represent the configuration for ElasticSearch."""

    user: str = env_field("ELASTIC_USER")
    password: str = env_field("ELASTIC_PASSWORD")
    servers: list[str] = env_field("ELASTIC_SERVERS", astype=json.loads)
    indexes: list[str] = env_field("ELASTIC_INDEXES", astype=json.loads)


@dataclass(frozen=True)
class DatabaseConfig(metaclass=Singleton):
    """Represent the configuration for a database."""

    host: str = env_field("DB_HOST")
    port: int = env_field("DB_PORT", astype=int)
    name: str = env_field("DB_NAME")
    username: str = env_field("DB_USER")
    password: str = env_field("DB_PASSWORD")
    schema: str = env_field("DB_SCHEMA", default="d2110")
    cache_refresh_interval: int = env_field("DB_CACHE_REFRESH_INTERVAL", astype=int, default="100")

    def to_conninfo(self) -> str:
        """Return a string representing the connection info for the postgres database."""
        return f"postgresql+psycopg://{self.username}:{self.password}@{self.host}:{self.port}/{self.name}"


@dataclass(frozen=True)
class CMDBConfig(metaclass=Singleton):
    """Represent the configuration for uCMDB database."""

    host: str = env_field("UCMDB_HOST")
    db_name: str = env_field("UCMDB_DB")
    username: str = env_field("UCMDB_USER")
    password: str = env_field("UCMDB_PASSWORD")

    def to_conninfo(self) -> str:
        """Return an url representing the connection info for the mssql database."""
        return f"mssql+pymssql://{self.username}:{urllib.parse.quote_plus(self.password)}@{self.host}/{self.db_name}"


@dataclass(frozen=True)
class KafkaConfig(metaclass=Singleton):
    """Represent the configuration for Kafka.

    Since Kafka consumers and producers share the same interface, this config can be used for both.
    Users should use the accompanying `KafkaConsumerConfig` for consumers and `KafkaProducerConfig` for producers.
    """

    user: str = env_field("KAFKA_USER")
    password: str = env_field("KAFKA_PASSWORD")
    topics: list[str] = env_field("KAFKA_TOPICS", astype=json.loads)
    bootstrap_servers: list[str] = env_field("KAFKA_BOOTSTRAP_SERVERS", astype=json.loads)
    security_protocol: str = env_field("KAFKA_SECURITY_PROTOCOL", default="SASL_SSL")
    sasl_mechanism: str = env_field("KAFKA_SASL_MECHANISM", default="PLAIN")
    message_encoding: str = env_field("KAFKA_MESSAGE_ENCODING", default="utf-8")
    log_level: int = env_field("KAFKA_LOG_LEVEL", astype=get_level_name, default="WARNING")

    def __post_init__(self):
        """Set the log level for the kafka logger and disable kafka.con warnings."""
        logging.getLogger("kafka").setLevel(self.log_level)
        warnings.filterwarnings("ignore", category=DeprecationWarning, module="kafka.conn")


@dataclass(frozen=True)
class KafkaConsumerConfig(KafkaConfig, metaclass=Singleton):
    """Represent the configuration specifically for Kafka Consumer.

    Notes
    -----
    This config should be used whenever a service consumes messages. All fields on `KafkaConfig` are inherited.
    """

    environment: str = env_field("KAFKA_ENVIRONMENT")
    consumer_timeout: float = env_field("KAFKA_CONSUMER_TIMEOUT", astype=float, default="10000")
    client_id_suffix: str = env_field("KAFKA_CLIENT_ID_SUFFIX")
    max_poll_interval_ms: int = env_field("KAFKA_MAX_POLL_INTERVAL_MS", astype=int, default="300000")
    max_poll_records: int = env_field("KAFKA_MAX_POLL_RECORDS", astype=int, default="1000")


@dataclass(frozen=True)
class KafkaProducerConfig(KafkaConfig, metaclass=Singleton):
    """Represent the configuration specifically for Kafka Producer.

    Notes
    -----
    This config should be used whenever a service produces messages. All fields on `KafkaConfig` are inherited.
    """

    retries: int = env_field("KAFKA_RETRIES", astype=int, default="10")
    max_in_flight_requests_per_connection: int = env_field("KAFKA_MAX_IN_FLIGHT_REQUESTS", astype=int, default="1")


@dataclass(frozen=True)
class LoggerConfig(metaclass=Singleton):
    """Represent a configuration for a logger.

    logs_max_size is expected to be configured in megabytes, but upon initializing, it will be converted to bytes.
    """

    debug_log_level: int = env_field("LOGS_DEBUG_LEVEL", astype=get_level_name, default="DEBUG")
    default_log_level: int = env_field("LOGS_DEFAULT_LEVEL", astype=get_level_name, default="INFO")
    logs_folder: Path = env_field("LOGS_FOLDER", astype=Path, default=pathstring("logs"))
    logs_max_size: int = env_field(key="LOGS_MAX_SIZE", astype=mb_to_bytes, default=str(MAX_FILE_SIZE_MB))


@dataclass(frozen=True)
class BaseServiceConfig(metaclass=Singleton):
    """Represent a base configuration for a service.

    This class defines the minimum required config variables.
    """

    debug: bool = env_field("DEBUG", astype=strtobool, default="0")
    logger_config: LoggerConfig = dataclass_field(LoggerConfig)
    service_name: str = env_field("OLYMPUS_SERVICE_NAME", default="Olympus")
    disable_monitoring_manager: bool = env_field("DISABLE_MONITORING_MANAGER", astype=strtobool, default="0")
    profiling_enabled: bool = env_field("PROFILING_ENABLED", astype=strtobool, default="0")


@dataclass(frozen=True)
class ServiceConfig(BaseServiceConfig, metaclass=Singleton):
    """Represent a configuration for a service.

    A service config is a minimal config including sleep_time and write_outputs.
    """

    sleep_time: int = env_field("SLEEP_TIME", astype=int, default="15")
    write_outputs: bool = env_field("WRITE_OUTPUTS", astype=strtobool, default="0")


@dataclass(frozen=True)
class KafkaConsumerServiceConfig(ServiceConfig, metaclass=Singleton):
    """Represent a ServiceConfig with a kafka consumer config added."""

    kafka_config: KafkaConsumerConfig = dataclass_field(KafkaConsumerConfig)


@dataclass(frozen=True)
class DatabaseKafkaConsumerServiceConfig(KafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent a KafkaConsumerServiceConfig with a database config added."""

    database_config: DatabaseConfig = dataclass_field(DatabaseConfig)


@dataclass(frozen=True)
class DatabaseServiceConfig(ServiceConfig, metaclass=Singleton):
    """Represent a ServiceConfig with a database config added."""

    database_config: DatabaseConfig = dataclass_field(DatabaseConfig)


@dataclass(frozen=True)
class KafkaProducerServiceConfig(ServiceConfig, metaclass=Singleton):
    """Represent a ServiceConfig with a kafka producer config added."""

    kafka_config: KafkaProducerConfig = dataclass_field(KafkaProducerConfig)


@dataclass(frozen=True)
class ExternalAPIConfig(metaclass=Singleton):
    """Represent the configuration for uCMDB database."""

    token_url: str = env_field("EXTERNAL_TOKEN_URL")
    client_id: str = env_field("EXTERNAL_CLIENT_ID")
    client_secret: str = env_field("EXTERNAL_CLIENT_SECRET")
    scope: str = env_field("EXTERNAL_SCOPE")
    api_url: str = env_field("EXTERNAL_URL")
    token_expiration: int = env_field("EXTERNAL_TOKEN_EXPIRATION", astype=int, default="3600")


@dataclass(frozen=True)
class ServerConfig(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for the server."""

    host: str = env_field("OLYMPUS_HOST", default="127.0.0.1")
    port: int = env_field("OLYMPUS_PORT", astype=int, default="8000")


@dataclass(frozen=True)
class JWKConfig(metaclass=Singleton):
    """Represent the configuration for JWK configuration."""

    uri: str = env_field("JWK_URI")
    algorithms: list[str] = env_field("JWK_ALGORITHMS", astype=json.loads, default='["RS256"]')
    cache_keys: bool = env_field("JWK_CACHE_KEYS", astype=strtobool, default="1")
    valid_audiences: list[str] = env_field("JWK_VALID_AUDIENCES", astype=json.loads)


@dataclass(frozen=True)
class ElasticApmConfig(metaclass=Singleton):
    """Represent the configuration for Elastic APM."""

    server_url: str = env_field("ELASTIC_APM_SERVER_URL")
    service_name: str = env_field("OLYMPUS_SERVICE_NAME")
    secret_token: str = env_field("ELASTIC_APM_SECRET_TOKEN")
    verify_server_cert: bool = env_field("ELASTIC_APM_VERIFY_SERVER_CERT", astype=strtobool, default="False")
    enable_elastic_apm: bool = env_field("ENABLE_ELASTIC_APM", astype=strtobool, default="False")
    environment: str = env_field("ELASTIC_APM_ENVIRONMENT", default="dev")
    # Disables capturing bodies
    capture_body: str = env_field("CAPTURE_BODY", default="errors")
    # Reduces the number of lines per stacktrace
    stack_trace_limit: int = env_field("STACK_TRACE_LIMIT", astype=int, default="5")
    # Use asynchronous mode to send trace to apm server
    transport_class: str = env_field("TRANSPORT_CLASS", default="elasticapm.transport.http.AsyncTransport")
    # Enables Gzip compression to reduce the size of data sent to Elastic APM.
    use_compression: bool = env_field("USE_COMPRESSION", astype=bool, default="1")
    # Disables capturing headers
    capture_headers: bool = env_field("CAPTURE_HEADERS", astype=bool, default="False")
    elastic_log_level: str = env_field("ELASTIC_APM_LOG_LEVEL", astype=get_level_name, default="INFO")

    @property
    def configure_client(self) -> dict:
        """Configure the elastic-apm client."""
        urllib3.disable_warnings()
        return {
            "SERVICE_NAME": self.service_name,
            "SERVER_URL": self.server_url,
            "SECRET_TOKEN": self.secret_token,
            "VERIFY_SERVER_CERT": self.verify_server_cert,
            "ENVIRONMENT": self.environment,
            "CAPTURE_BODY": self.capture_body,
            "STACK_TRACE_LIMIT": self.stack_trace_limit,
            "ELASTIC_APM_LOG_LEVEL": self.elastic_log_level,
            "ELASTIC_APM_CLOUD_PROVIDER": "none",  # ignore cloud metadata
            "COLLECT_LOCAL_VARIABLES": "errors",
            "SOURCE_LINES_ERROR_APP_FRAMES": 5,  # reduce the number of lines per stacktrace
            "SOURCE_LINES_ERROR_LIBRARY_FRAMES": 0,  # disable library source
            "TRANSPORT_CLASS": self.transport_class,
            "TRANSPORT_OPTIONS": {
                "use_compression": self.use_compression,
                "timeout": 10,  # longer timeout in case of network issues
                "max_batch_size": 200,  # smaller batch size for better performance
                "max_retries": 3,  # more retries for reliability
                "flush_interval": 10,
            },
        }
