"""Module that provides a custom LoggingHandler for Elastic APM."""

import logging

import elasticapm


class ElasticApmLoggingHandler(logging.Handler):
    """Custom logging handler for Elastic APM.

    This handler replaces the deprecated elasticapm.handlers.logging.LoggingHandler
    while maintaining the same core functionality. It sends log records to Elastic APM
    as errors or messages depending on their level.

    Parameters
    ----------
    client : elasticapm.Client
        The Elastic APM client instance to use for sending logs.
    level : int, optional
        The minimum log level to process, by default logging.NOTSET.
    """

    # Standard logging record attributes that should not be included in custom data
    _RESERVED_ATTRS = frozenset(
        {
            "name",
            "msg",
            "args",
            "levelname",
            "levelno",
            "pathname",
            "filename",
            "module",
            "exc_info",
            "exc_text",
            "stack_info",
            "lineno",
            "funcName",
            "created",
            "msecs",
            "relativeCreated",
            "thread",
            "threadName",
            "processName",
            "process",
            "getMessage",
            "message",
        }
    )

    def __init__(self, client: elasticapm.Client, level: int = logging.NOTSET) -> None:
        """Initialize the handler with an Elastic APM client."""
        super().__init__(level)
        self.client = client

    def emit(self, record: logging.LogRecord) -> None:
        """Process a log record and send it to Elastic APM.

        Parameters
        ----------
        record : logging.LogRecord
            The log record to process.
        """
        try:
            # Skip if client is not available
            if not self.client:
                return

            if record.levelno >= logging.ERROR:
                # For error logs, capture as exceptions
                self._emit_error(record)
            else:
                # For non-error logs, send as custom messages
                self._emit_message(record)
        except Exception:
            self.handleError(record)

    def _emit_error(self, record: logging.LogRecord) -> None:
        """Send error log records to Elastic APM as exceptions.

        Parameters
        ----------
        record : logging.LogRecord
            The log record to process.
        """
        kwargs = {
            "message": self.format(record),
            "handled": True,
            "custom": self._get_custom_data(record),
        }

        # Include param_message if available
        if hasattr(record, "message"):
            kwargs["param_message"] = record.message

        # If there's an actual exception, include it
        if record.exc_info:
            kwargs["exc_info"] = record.exc_info

        self.client.capture_exception(**kwargs)

    def _emit_message(self, record: logging.LogRecord) -> None:
        """Send non-error log records to Elastic APM as messages.

        Parameters
        ----------
        record : logging.LogRecord
            The log record to process.
        """
        kwargs = {
            "message": self.format(record),
            "custom": self._get_custom_data(record),
            "level": self._get_apm_level(record.levelno),
        }

        # Include param_message if available
        if hasattr(record, "message"):
            kwargs["param_message"] = record.message

        self.client.capture_message(**kwargs)

    def _get_custom_data(self, record: logging.LogRecord) -> dict:
        """Extract custom data from the log record.

        Parameters
        ----------
        record : logging.LogRecord
            The log record to process.

        Returns
        -------
        dict
            A dictionary of custom data extracted from the log record.
        """
        custom = {
            "logger_name": record.name,
            "module": record.module,
            "function": record.funcName,
            "lineno": record.lineno,
            "process": record.process,
            "thread": record.thread,
            "pathname": record.pathname,
        }

        # Include any extra attributes from the record (excluding reserved ones)
        for key, value in record.__dict__.items():
            if key not in self._RESERVED_ATTRS and not key.startswith("_"):
                custom[key] = value

        return custom

    def _get_apm_level(self, level: int) -> str:
        """Convert a Python logging level to an Elastic APM level.

        Parameters
        ----------
        level : int
            The Python logging level.

        Returns
        -------
        str
            The corresponding Elastic APM level.
        """
        # Handle standard levels
        if level >= logging.CRITICAL:
            return "critical"
        elif level >= logging.ERROR:
            return "error"
        elif level >= logging.WARNING:
            return "warning"
        elif level >= logging.INFO:
            return "info"
        else:
            return "debug"
